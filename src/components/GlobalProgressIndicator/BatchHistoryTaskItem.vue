<template>
  <div class="batch-history-task">
    <!-- 批量任务头部 -->
    <div class="batch-task-header" @click="toggleExpanded">
      <div class="batch-task-icon">
        <component :is="batchIcon" class="w-4 h-4" :class="batchIconClass" />
      </div>

      <div class="batch-task-info">
        <div class="batch-task-title">
          <span class="batch-name">{{ batchTask.batchName }}</span>
          <span class="batch-stats">
            ({{ batchTask.completedFiles }}/{{ batchTask.totalFiles }} 文件)
          </span>
        </div>

        <div class="batch-task-meta">
          <span class="batch-size">{{ formatFileSize(batchTask.totalSize) }}</span>
          <span class="batch-status" :class="statusClass">{{ statusText }}</span>
          <span class="batch-time">{{ formatDate(batchTask.endTime) }}</span>
          <span>·</span>
          <span>{{ formatDuration(batchTask.duration) }}</span>
        </div>
      </div>

      <div class="batch-task-actions">
        <!-- 展开/折叠按钮 -->
        <Button variant="ghost" size="sm" class="expand-button">
          <ChevronDown class="w-4 h-4 transition-transform" :class="{ 'rotate-180': expanded }" />
        </Button>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <Button v-if="batchTask.status === 'error'" variant="ghost" size="sm"
            @click.stop="$emit('retry', batchTask.id)" class="p-0 w-6 h-6" title="重试">
            <RotateCcwIcon class="w-3 h-3" />
          </Button>
          <Button variant="ghost" size="sm" @click.stop="$emit('remove', batchTask.id)" class="p-0 w-6 h-6" title="删除">
            <XIcon class="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 子任务列表 -->
    <Transition name="sub-tasks" mode="out-in" @after-enter="onTransitionEnd" @after-leave="onTransitionEnd">
      <div v-if="expanded" class="sub-tasks-container">
        <div class="sub-tasks-header">
          <span class="sub-tasks-title">文件详情</span>
          <span class="sub-tasks-count">{{ batchTask.subTasks.length }} 个文件</span>
        </div>

        <div class="sub-tasks-list">
          <div v-for="subTask in batchTask.subTasks" :key="subTask.id" class="sub-task-item">
            <div class="sub-task-icon">
              <FileIcon class="w-3 h-3 text-muted-foreground" />
            </div>

            <div class="sub-task-info">
              <div class="sub-task-name">{{ subTask.fileName }}</div>
              <div class="sub-task-meta">
                <span class="file-size">{{ formatFileSize(subTask.fileSize || 0) }}</span>
                <span class="task-status" :class="getSubTaskStatusClass(subTask.status)">
                  {{ getSubTaskStatusText(subTask.status) }}
                </span>
              </div>
            </div>

            <div class="sub-task-status">
              <div class="status-indicator" :class="getSubTaskStatusClass(subTask.status)">
                <Check v-if="subTask.status === 'completed'" class="w-3 h-3" />
                <XIcon v-else-if="subTask.status === 'error'" class="w-3 h-3" />
                <AlertCircle v-else-if="subTask.status === 'cancelled'" class="w-3 h-3" />
                <Clock v-else class="w-3 h-3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  FolderOpen, FolderDown, ChevronDown, RotateCcw as RotateCcwIcon, X as XIcon,
  FileIcon, Check, AlertCircle, Clock
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import {
  getStatusClass,
  getStatusText,
  formatFileSize,
  formatDate,
  formatDuration
} from '@/lib/upload-utils'

import type { BatchHistoryTask } from '@/composables/useGlobalProgress'

// Props
const props = defineProps<{
  batchTask: BatchHistoryTask
  expanded?: boolean
}>()

// Emits
const emit = defineEmits<{
  'retry': [batchId: string]
  'remove': [batchId: string]
  'transition-end': []
  'toggle-expanded': [batchId: string]
}>()

// 过渡动画结束处理
const onTransitionEnd = () => {
  emit('transition-end')
}

// 使用 prop 中的展开状态，如果没有提供则使用本地状态
const expanded = computed(() => props.expanded ?? false)

// 切换展开状态
const toggleExpanded = () => {
  // 通知父组件切换展开状态
  emit('toggle-expanded', props.batchTask.id)
}

// 状态计算 - 使用工具函数
const statusClass = computed(() => getStatusClass(props.batchTask.status))
const statusText = computed(() => getStatusText(props.batchTask.status))

// 计算批量任务图标
const batchIcon = computed(() => {
  return props.batchTask.batchType === 'upload' ? FolderOpen : FolderDown
})

// 计算批量任务图标样式
const batchIconClass = computed(() => {
  return props.batchTask.batchType === 'upload' ? 'text-blue-500' : 'text-green-500'
})

// 子任务状态工具函数
const getSubTaskStatusClass = (status: string) => getStatusClass(status as any)
const getSubTaskStatusText = (status: string) => getStatusText(status as any)
</script>

<style scoped>
.batch-history-task {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.batch-task-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  cursor: pointer;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.batch-task-header:hover {
  background-color: hsl(var(--accent));
}

.batch-task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  background-color: hsl(var(--primary) / 0.1);
}

.batch-task-info {
  flex: 1;
  min-width: 0;
}

.batch-task-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.125rem;
}

.batch-name {
  font-weight: 500;
  font-size: 0.75rem;
  color: hsl(var(--foreground));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.batch-stats {
  font-size: 0.7rem;
  color: hsl(var(--muted-foreground));
  font-weight: 500;
}

.batch-task-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.625rem;
}

.batch-size {
  color: hsl(var(--muted-foreground));
}

.batch-status {
  font-weight: 500;
}

.batch-time {
  color: hsl(var(--muted-foreground));
}

.batch-task-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.125rem;
}

.expand-button {
  padding: 0.125rem;
  width: 1rem;
  height: 1rem;
}

/* 子任务样式 */
.sub-tasks-container {
  border-top: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.3);
}

.sub-tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.5rem;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.5);
}

.sub-tasks-title {
  font-weight: 600;
  font-size: 0.7rem;
  color: hsl(var(--foreground));
}

.sub-tasks-count {
  font-size: 0.625rem;
  color: hsl(var(--muted-foreground));
}

.sub-tasks-list {
  max-height: 120px;
  overflow-y: auto;
}

.sub-task-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  gap: 0.375rem;
  border-bottom: 1px solid hsl(var(--border));
  transition: background-color 0.2s ease;
}

.sub-task-item:hover {
  background-color: hsl(var(--accent));
}

.sub-task-item:last-child {
  border-bottom: none;
}

.sub-task-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.sub-task-info {
  flex: 1;
  min-width: 0;
}

.sub-task-name {
  font-size: 0.7rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.0625rem;
}

.sub-task-meta {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.625rem;
}

.file-size {
  color: hsl(var(--muted-foreground));
}

.task-status {
  font-weight: 500;
}

.sub-task-status {
  display: flex;
  align-items: center;
  min-width: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.2;
}

.status-indicator svg {
  color: white;
  opacity: 1;
}

/* 自定义滚动条 */
.sub-tasks-list::-webkit-scrollbar {
  width: 4px;
}

.sub-tasks-list::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 2px;
}

.sub-tasks-list::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 2px;
}

.sub-tasks-list::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* 子任务展开/折叠过渡动画 */
.sub-tasks-enter-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.sub-tasks-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.sub-tasks-enter-from {
  max-height: 0;
  opacity: 0;
}

.sub-tasks-enter-to {
  max-height: 200px;
  /* 与子任务容器的实际最大高度保持一致 */
  opacity: 1;
}

.sub-tasks-leave-from {
  max-height: 200px;
  opacity: 1;
}

.sub-tasks-leave-to {
  max-height: 0;
  opacity: 0;
}
</style>