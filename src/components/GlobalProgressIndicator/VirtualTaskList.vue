<template>
  <div ref="containerRef" class="virtual-task-list" :style="{ height: `${height}px`, overflow: 'auto' }">
    <div :style="{ height: `${totalSize}px`, position: 'relative' }">
      <div v-for="virtualItem in virtualItems" :key="virtualItem.key" :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: `${virtualItem.size}px`,
        transform: `translateY(${virtualItem.start}px)`
      }">
        <slot :item="items[virtualItem.index]" :index="virtualItem.index" :virtualItem="virtualItem" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface VirtualItem {
  key: string | number
  index: number
  start: number
  size: number
}

interface Props {
  items: T[]
  height: number
  estimateSize?: number
  overscan?: number
  getItemKey?: (item: T, index: number) => string | number
  getItemSize?: (item: T, index: number) => number
}

const props = withDefaults(defineProps<Props>(), {
  estimateSize: 60,
  overscan: 5,
  getItemKey: (_item: T, index: number) => index,
  getItemSize: undefined
})

const containerRef = ref<HTMLElement>()

// 使用简单的虚拟滚动实现，避免 @tanstack/vue-virtual 的API问题
const scrollTop = ref(0)

// 动态计算项目高度
const getItemHeight = (index: number) => {
  if (props.getItemSize && index < props.items.length) {
    return props.getItemSize(props.items[index], index)
  }
  return props.estimateSize
}

// 计算总高度
const totalSize = computed(() => {
  if (props.getItemSize) {
    let total = 0
    for (let i = 0; i < props.items.length; i++) {
      total += getItemHeight(i)
    }
    return total
  }
  return props.items.length * props.estimateSize
})

// 计算可见区域
const visibleRange = computed(() => {
  if (props.items.length === 0) return { start: 0, end: -1 }

  if (props.getItemSize) {
    // 动态高度计算
    let currentOffset = 0
    let startIndex = 0
    let endIndex = props.items.length - 1

    // 找到开始索引
    for (let i = 0; i < props.items.length; i++) {
      const itemHeight = getItemHeight(i)
      if (currentOffset + itemHeight > scrollTop.value) {
        startIndex = i
        break
      }
      currentOffset += itemHeight
    }

    // 找到结束索引
    currentOffset = 0
    for (let i = 0; i < props.items.length; i++) {
      const itemHeight = getItemHeight(i)
      currentOffset += itemHeight
      if (currentOffset >= scrollTop.value + props.height) {
        endIndex = i
        break
      }
    }

    // 添加 overscan
    const overscanStart = Math.max(0, startIndex - props.overscan)
    const overscanEnd = Math.min(props.items.length - 1, endIndex + props.overscan)

    return {
      start: overscanStart,
      end: overscanEnd
    }
  } else {
    // 固定高度计算
    const containerHeight = props.height
    const startIndex = Math.floor(scrollTop.value / props.estimateSize)
    const endIndex = Math.min(
      props.items.length - 1,
      Math.ceil((scrollTop.value + containerHeight) / props.estimateSize)
    )

    // 添加 overscan
    const overscanStart = Math.max(0, startIndex - props.overscan)
    const overscanEnd = Math.min(props.items.length - 1, endIndex + props.overscan)

    return {
      start: overscanStart,
      end: overscanEnd
    }
  }
})

// 计算可见项目
const virtualItems = computed<VirtualItem[]>(() => {
  const { start, end } = visibleRange.value
  const items: VirtualItem[] = []

  if (start > end) return items

  if (props.getItemSize) {
    // 动态高度计算
    let currentOffset = 0

    // 计算到开始位置的偏移量
    for (let i = 0; i < start; i++) {
      currentOffset += getItemHeight(i)
    }

    // 生成可见项目
    for (let i = start; i <= end; i++) {
      if (i < props.items.length) {
        const size = getItemHeight(i)
        items.push({
          key: props.getItemKey(props.items[i], i),
          index: i,
          start: currentOffset,
          size
        })
        currentOffset += size
      }
    }
  } else {
    // 固定高度计算
    for (let i = start; i <= end; i++) {
      if (i < props.items.length) {
        items.push({
          key: props.getItemKey(props.items[i], i),
          index: i,
          start: i * props.estimateSize,
          size: props.estimateSize
        })
      }
    }
  }

  return items
})

// 处理滚动事件
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  if (target) {
    scrollTop.value = target.scrollTop
  }
}

// 监听 items 变化，虚拟列表会自动响应数据变化
watch(() => props.items.length, () => {
  // 虚拟列表会自动重新计算可见区域
}, { flush: 'post' })

// 滚动对齐选项类型
type ScrollAlign = 'start' | 'center' | 'end' | 'auto'

// 提供滚动到指定项的方法
const scrollToItem = (index: number, options?: { align?: ScrollAlign }) => {
  if (!containerRef.value || index < 0 || index >= props.items.length) return

  let itemOffset: number
  let itemSize: number

  if (props.getItemSize) {
    // 动态高度计算
    itemOffset = 0
    for (let i = 0; i < index; i++) {
      itemOffset += getItemHeight(i)
    }
    itemSize = getItemHeight(index)
  } else {
    // 固定高度计算
    itemOffset = index * props.estimateSize
    itemSize = props.estimateSize
  }

  let scrollOffset = itemOffset

  if (options?.align === 'center') {
    scrollOffset = itemOffset - (props.height - itemSize) / 2
  } else if (options?.align === 'end') {
    scrollOffset = itemOffset - props.height + itemSize
  }

  scrollOffset = Math.max(0, Math.min(scrollOffset, totalSize.value - props.height))
  containerRef.value.scrollTop = scrollOffset
}

// 提供滚动到顶部的方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 提供滚动到底部的方法
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = totalSize.value
  }
}

// 强制更新虚拟列表
const forceUpdate = () => {
  // 触发响应式更新，重新计算可见区域
  scrollTop.value = scrollTop.value
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  forceUpdate
})

// 存储事件监听器引用以便清理
let scrollEventCleanup: (() => void) | null = null

// 组件挂载时初始化
onMounted(() => {
  // 添加滚动事件监听器
  if (containerRef.value) {
    const element = containerRef.value
    element.addEventListener('scroll', handleScroll, { passive: true })

    // 保存清理函数
    scrollEventCleanup = () => {
      element.removeEventListener('scroll', handleScroll)
    }
  }
})

// 性能优化：在组件卸载时清理
onUnmounted(() => {
  if (scrollEventCleanup) {
    scrollEventCleanup()
    scrollEventCleanup = null
  }
})
</script>

<style scoped>
.virtual-task-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.virtual-task-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-task-list::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-task-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.virtual-task-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}
</style>
